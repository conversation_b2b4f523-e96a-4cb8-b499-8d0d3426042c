import IconBadge from '@/components/IconBadge'
import { Minimize2, Maximize2, MapPin } from 'lucide-react'
import pluralize from 'pluralize'

type Props = {
  recommendationsLength: number
  isFullscreen: boolean
  toggleFullscreen: () => void
}

const MapOverlay = ({ recommendationsLength, isFullscreen, toggleFullscreen }: Props) => (
  <>
    <button
      onClick={toggleFullscreen}
      className="absolute top-4 left-4 z-20 p-2 bg-white border border-white/20 rounded-lg shadow-lg text-slate-700"
    >
      {isFullscreen ? <Minimize2 className="size-5" /> : <Maximize2 className="size-5" />}
    </button>

    <IconBadge
      Icon={MapPin}
      text={`${recommendationsLength} ${pluralize('place', recommendationsLength)}`}
      variant="default"
      className="!bg-white absolute bottom-4 right-4 z-20 py-1 shadow-md"
      size="sm"
    />
  </>
)

export default MapOverlay
