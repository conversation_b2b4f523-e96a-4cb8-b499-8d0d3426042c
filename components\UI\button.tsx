import * as React from 'react'
import { Slot } from '@radix-ui/react-slot'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/lib/utils'

type ButtonProps = React.ComponentProps<'button'> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean
  }

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full font-medium text-sm !transition-everything disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20  dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground shadow-xs hoverActive:bg-primary/90',
        destructive:
          'bg-destructive text-white shadow-xs hoverActive:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',
        outline:
          'border bg-background shadow-xs hoverActive:bg-accent hoverActive:text-accent-foreground dark:bg-input/30 dark:border-input dark:hoverActive:bg-input/50',
        secondary: 'bg-secondary text-secondary-foreground shadow-xs hoverActive:bg-secondary/80',
        ghost:
          'hoverActive:bg-accent hoverActive:text-accent-foreground dark:hoverActive:bg-accent/50',
        link: 'text-primary underline-offset-4 hoverActive:underline',
        glass:
          'hoverActive:bg-white/20 text-primary-text hoverActive:text-secondary-text border-[1px] border-glass-border shadow-sm hoverActive:shadow-md',
        frostedGlass:
          'bg-white/15 hoverActive:bg-white/30 backdrop-blur-md border-[1px] border-glass-border text-primary-text shadow-sm hoverActive:shadow-md',
        card: 'bg-card-bg hoverActive:bg-card-hover border-[1px] border-glass-border text-primary-text shadow-sm hoverActive:shadow-md',
      },
      size: {
        default: 'h-9 px-4 py-2',
        sm: 'h-8 gap-1.5 px-3',
        lg: 'h-10 px-6',
        icon: 'size-9',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)

const Button = ({ className, variant, size, asChild = false, ...props }: ButtonProps) => {
  const Comp = asChild ? Slot : 'button'

  return (
    <Comp
      data-slot="button"
      type="button"
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  )
}

export { Button, buttonVariants, type ButtonProps }
