import getScrollConfig from '@/lib/getHeaderScrollConfig'
import { LucideIcon } from 'lucide-react'

export type Recommendation = {
  id: string
  name: string
  description: string
  rating: number
  priceLevel: number
  category: string
  address: string
  hours: string
  image: string
  tags: string[]
  phone: string | null
  website: string
  detailedDescription: string
  amenities: string[]
  images: string[]
  reviews: {
    id: string
    author: string
    rating: number
    comment: string
    date: string
  }[]
  coordinates: {
    lat: number
    lng: number
  }
  email: string | null
  social: {
    instagram?: string
    facebook?: string
  }
  googleMaps: string
  openingHours: Record<string, string> | null
  menuHighlights: string[]
  recommendedTravel: string
  travelTime: number
}

export const themes = ['light', 'dark'] as const

export type Theme = (typeof themes)[number]

export const categories = ['eat', 'drink', 'do'] as const

export type Category = (typeof categories)[number]

export type CategoryDetails = {
  id: Category
  name: string
  title: string
  description: string
  subtitle: string
  gradient: string
  bgGradient: string
  shadowColor: string
  icon: LucideIcon
  accentIcon: LucideIcon
  emoji: string
  iconColor: string
}

export const recommendationListTypes = ['grid', 'list'] as const

export type RecommendationListType = (typeof recommendationListTypes)[number]

export type ScrollConfig = ReturnType<typeof getScrollConfig>

export type TravelTimeCategory = {
  minutes: number
  text: string
}

export type FilterState = {
  priceLevel: number[]
  rating: number
  tags: string[]
  amenities: string[]
  travelTime: TravelTimeCategory
  categories: string[]
}
