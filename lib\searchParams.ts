import {
  parseAsFloat,
  create<PERSON>oader,
  parseAsArrayOf,
  parseAsInteger,
  parseAsJson,
  parseAsString,
} from 'nuqs/server'
import { z } from 'zod'
import { defaultFilterState } from '@/lib/filterUtils'

export const filterSchema = z.object({
  priceLevel: z.array(z.number()),
  rating: z.number().min(0).max(5),
  tags: z.array(z.string()),
  categories: z.array(z.string()),
  travelTime: z.object({
    minutes: z.number().min(0),
    text: z.string(),
  }),
  amenities: z.array(z.string()),
})

export type FilterFormType = z.infer<typeof filterSchema>

export const queryFiltersSearchParams = {
  q: parseAsString.withDefault(''),
  priceLevel: parseAsArrayOf(parseAsInteger).withDefault([]),
  rating: parseAsFloat.withDefault(0),
  tags: parseAsArrayOf(parseAsString).withDefault([]),
  categories: parseAsArrayOf(parseAsString).withDefault([]),
  travelTime: parseAsJson(filterSchema.shape.travelTime.parse).withDefault(
    defaultFilterState.travelTime
  ),
  amenities: parseAsArrayOf(parseAsString).withDefault([]),
} as const

export const queryFilterLoader = createLoader(queryFiltersSearchParams)
