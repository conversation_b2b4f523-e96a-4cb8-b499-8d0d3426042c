import { Recommendation } from '@/types/types'
import DetailSection from './DetailSection'
import SocialButton from '@/components/SocialButton'
import { Facebook, Instagram, Zap } from 'lucide-react'

type Props = {
  iconColor: string
  recommendation: Recommendation
}

const Footer = ({
  iconColor,
  recommendation: {
    social: { instagram, facebook },
  },
}: Props) => (
  <DetailSection
    Icon={Zap}
    title="Follow Us"
    as="footer"
    className="max-w-md w-full mx-auto"
    iconColor={iconColor}
    animationDelay={600}
  >
    <div className="inline-flex gap-2 pb-8">
      {instagram && (
        <SocialButton
          link={instagram}
          Icon={Instagram}
          text="Instagram"
          className="from-pink-500 to-purple-600 hoverActive:from-pink-600 hoverActive:to-purple-700"
        />
      )}
      {facebook && (
        <SocialButton
          link={facebook}
          Icon={Facebook}
          text="Facebook"
          className="from-blue-600 to-blue-700 hoverActive:from-blue-700 hoverActive:to-blue-800"
        />
      )}
    </div>
  </DetailSection>
)

export default Footer
