'use client'

import { cn } from '@/lib/utils'
import { Filter, MapPin, Sparkles } from 'lucide-react'
import IconBadge from '@/components/IconBadge'
import IconButton from '@/components/IconButton'
import { Category, FilterState } from '@/types/types'
import { getCategoryDetailsById } from '@/lib/categoryHelper'
import { useState } from 'react'
import SearchToggle from './SearchToggle'
import { useFilterDrawer } from '@/store/filterDrawer.store'
import { getActiveFiltersCount } from '@/lib/filterUtils'
import pluralize from 'pluralize'

type Props = {
  category: Category
  numberOfRecommendations: number
  searchParamsFilters: FilterState
}

const RecommendationListHeader = ({
  category,
  numberOfRecommendations,
  searchParamsFilters,
}: Props) => {
  const setIsFilterDrawerOpen = useFilterDrawer(state => state.setIsFilterDrawerOpen)
  const [{ gradient, bgGradient, subtitle }] = useState(() => getCategoryDetailsById(category))

  const activeFiltersCount = getActiveFiltersCount(searchParamsFilters)

  return (
    <section className="relative overflow-hidden text-center **:duration-300">
      <div className={cn('absolute inset-0 bg-gradient-to-br transition-colors', bgGradient)} />
      <div className="absolute inset-0 backdrop-blur-sm bg-white/5" />
      <div className="relative flex flex-col gap-6 px-6 py-12 animation-duration-1000 opacity-0 animate-fade-up max-w-md mx-auto">
        <h2 className="text-lg font-bold transition-colors">{subtitle}</h2>
        <div className="flex items-center justify-center gap-4">
          <IconBadge
            Icon={MapPin}
            text={`${numberOfRecommendations} ${pluralize('place', numberOfRecommendations)}`}
            className="hoverActive:scale-105 border-glass-border"
            size="lg"
          />
          <IconBadge
            Icon={Sparkles}
            text="Curated"
            className="hoverActive:scale-105 border-glass-border"
            iconClassName="text-yellow-400"
            size="lg"
          />
        </div>
        <div className="flex items-center justify-center gap-4">
          <SearchToggle category={category} />
          <IconButton
            size="sm"
            variant="frostedGlass"
            text="Filter"
            Icon={Filter}
            badgeText={!!activeFiltersCount ? activeFiltersCount : undefined}
            className={cn(
              'hoverActive:scale-105',
              activeFiltersCount && 'bg-gradient-to-r text-white border-x-0 gap-3',
              gradient
            )}
            onClick={() => setIsFilterDrawerOpen(true)}
          />
        </div>
      </div>
    </section>
  )
}

export default RecommendationListHeader
