import { Clock, Globe, MapPin, Navigation, Phone, Mail } from 'lucide-react'
import { cn } from '@/lib/utils'
import IconButton from '@/components/IconButton'
import GlassCard from '@/components/GlassCard'
import { CategoryDetails, Recommendation } from '@/types/types'
import DetailSection from './DetailSection'

type RecommendationProps = Pick<
  Recommendation,
  'address' | 'hours' | 'openingHours' | 'phone' | 'website' | 'email' | 'googleMaps'
>

type Props = RecommendationProps & {
  iconColor: CategoryDetails['iconColor']
  gradient: CategoryDetails['gradient']
}

const ContactsHoursSection = ({
  gradient,
  iconColor,
  address,
  hours,
  openingHours,
  phone,
  website,
  email,
  googleMaps,
}: Props) => (
  <DetailSection Icon={MapPin} title="Contact & Hours" iconColor={iconColor} animationDelay={300}>
    <GlassCard variant="frostedGlass" className="gap-12">
      <div className="space-y-4 **:transition-colors **:duration-300">
        <div className="flex items-center text-secondary-text gap-3 text-base">
          <MapPin size={16} className="text-accent-text shrink-0" />
          {address}
        </div>
        <div className={cn('flex gap-3 text-base', openingHours ? 'items-start' : 'items-center')}>
          <Clock size={16} className="text-accent-text shrink-0" />
          {!openingHours ? (
            <span>{hours || 'Hours not available'}</span>
          ) : (
            <div className="flex-1 space-y-1">
              {Object.entries(openingHours).map(([day, open_hours]) => (
                <div key={day} className="flex justify-between text-sm">
                  <span className="font-medium text-secondary-text">{day}:</span>
                  <span className="text-accent-text">{open_hours}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
      {(googleMaps || phone || website || email) && (
        <div className="grid grid-cols-2 gap-3 *:duration-700">
          {googleMaps && (
            <IconButton
              className={cn(
                'bg-gradient-to-r hoverActive:scale-105 text-white border-0 shadow-lg hoverActive:shadow-xl',
                gradient
              )}
              Icon={Navigation}
              text="Directions"
              link={googleMaps}
            />
          )}
          {phone && (
            <IconButton
              variant="frostedGlass"
              className="hoverActive:scale-105"
              Icon={Phone}
              text="Call"
              link={`tel:${phone}`}
            />
          )}
          {website && website !== '#' && (
            <IconButton
              variant="frostedGlass"
              className="hoverActive:scale-105"
              Icon={Globe}
              text="Website"
              link={website}
            />
          )}
          {email && (
            <IconButton
              variant="frostedGlass"
              className="hoverActive:scale-105"
              Icon={Mail}
              text="Email"
              link={`mailto:${email}`}
            />
          )}
        </div>
      )}
    </GlassCard>
  </DetailSection>
)

export default ContactsHoursSection
