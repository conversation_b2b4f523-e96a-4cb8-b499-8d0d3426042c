import Link from 'next/link'
import { Button, type ButtonProps } from '@/components/UI/button'
import { isExternalURL } from '@/lib/isExternalURL'
import { LucideIcon } from 'lucide-react'
import IconWithTextAndBadge from '@/components/IconWithTextAndBadge'

type Props = ButtonProps & {
  Icon: LucideIcon
  text?: string
  iconSize?: number
  iconClassName?: string
  link?: string
  badgeText?: string | number
}

const IconButton = ({
  text,
  Icon,
  link,
  variant = 'glass',
  iconSize,
  iconClassName,
  badgeText,
  ...props
}: Props) => (
  <Button variant={variant} {...props} {...(link && { asChild: true })}>
    {link ? (
      <Link
        href={link}
        {...(isExternalURL(link) && { target: '_blank', rel: 'noopener noreferrer' })}
      >
        <IconWithTextAndBadge
          Icon={Icon}
          text={text}
          badgeText={badgeText}
          iconClassName={iconClassName}
          size={iconSize}
        />
      </Link>
    ) : (
      <>
        <IconWithTextAndBadge
          Icon={Icon}
          text={text}
          badgeText={badgeText}
          iconClassName={iconClassName}
          size={iconSize}
        />
      </>
    )}
  </Button>
)

export default IconButton
