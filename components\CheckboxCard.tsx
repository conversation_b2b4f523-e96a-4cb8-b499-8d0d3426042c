import { Checkbox } from '@/components/UI/checkbox'
import { cn } from '@/lib/utils'

const defaultClasses = {
  sm: 'gap-1.5 px-2 py-0.5 rounded-md hoverActive:scale-105 text-secondary-text text-xs',
  default: 'gap-3 p-3 rounded-2xl hoverActive:scale-102 text-primary-text text-sm',
}

type CheckboxTagProps = {
  checked: boolean
  onCheckedChange: (checked: boolean) => void
  label: string
  name: string
  gradient?: string
  size?: 'sm' | 'default'
}

const CheckboxTag = ({
  checked,
  onCheckedChange,
  gradient,
  name,
  label,
  size = 'default',
}: CheckboxTagProps) => (
  <label
    className={cn(
      'flex items-center capitalize transition-everything duration-300 bg-card-bg hoverActive:bg-card-hover border-[1px] border-glass-border cursor-pointer shadow-sm hoverActive:shadow-md',
      defaultClasses[size],
      checked && 'bg-gradient-to-r text-white border-x-0 ' + gradient
    )}
  >
    <Checkbox
      checked={checked}
      name={name}
      onCheckedChange={onCheckedChange}
      className={cn(size === 'sm' && 'sr-only')}
    />
    <span className="font-medium">{label}</span>
  </label>
)

export default CheckboxTag
