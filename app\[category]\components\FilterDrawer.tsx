'use client'

import React, { useEffect } from 'react'
// import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import { SubmitHandler, useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useQueryStates } from 'nuqs'
import isEqual from 'lodash/isEqual'
import {
  Drawer,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
  DrawerDescription,
} from '@/components/UI/drawer'
import { Form, FormField, FormItem, FormControl } from '@/components/UI/form'
import { Slider } from '@/components/UI/slider'
import { RadioGroup } from '@/components/UI/radio-group'
import IconButton from '@/components/IconButton'
import CheckboxCard from '@/components/CheckboxCard'
import DrawerSection from './DrawerSection'
import { X, Filter, RotateCcw } from 'lucide-react'
import {
  getFilterOptions,
  getPriceLevelText,
  getActiveFiltersCount,
  getNumberFromText,
  defaultFilterState,
} from '@/lib/filterUtils'
import { getCategoryDetailsById } from '@/lib/categoryHelper'
import { drawerTitles } from '@/data/categories'
import { useFilterDrawer } from '@/store/filterDrawer.store'
import { useShallow } from 'zustand/react/shallow'
import { Category, Recommendation } from '@/types/types'
import { cn } from '@/lib/utils'
import RadioCard from '@/components/RadioCard'
import pluralize from 'pluralize'
import { queryFiltersSearchParams, filterSchema, FilterFormType } from '@/lib/searchParams'

type Props = {
  category: Category
  recommendations: Recommendation[]
}

const FilterDrawer = ({ recommendations, category }: Props) => {
  const { isFilterDrawerOpen, setIsFilterDrawerOpen } = useFilterDrawer(
    useShallow(state => ({
      isFilterDrawerOpen: state.isFilterDrawerOpen,
      setIsFilterDrawerOpen: state.setIsFilterDrawerOpen,
    }))
  )
  const filterOptions = getFilterOptions(recommendations)
  const { gradient } = getCategoryDetailsById(category)
  const [queryFilters, setQueryFilters] = useQueryStates(queryFiltersSearchParams, {
    history: 'push',
  })

  const form = useForm<FilterFormType>({
    resolver: zodResolver(filterSchema),
    defaultValues: queryFilters,
  })

  const { handleSubmit, watch, reset, control } = form
  const filters = watch()
  const activeFiltersCount = getActiveFiltersCount(filters)

  const handleClose = () => setIsFilterDrawerOpen(false)

  const handleClickResetAll = () => reset(defaultFilterState)

  const handleClickResetLocal = () => reset(queryFilters)

  const onSubmit: SubmitHandler<FilterFormType> = data => {
    setQueryFilters(data)
    setIsFilterDrawerOpen(false)
  }

  useEffect(() => {
    if (isFilterDrawerOpen) reset(queryFilters)
  }, [queryFilters, isFilterDrawerOpen, reset])

  return (
    <Drawer open={isFilterDrawerOpen} onClose={handleClose}>
      <DrawerContent
        className="max-h-[85vh] bg-card-bg backdrop-blur-xl border border-muted-foreground/40"
        thumbClassName="bg-secondary-text"
      >
        <DrawerHeader className="flex-row items-center justify-between bg-card-bg backdrop-blur-xl border-b border-muted-foreground/40 sticky top-0 z-10 mt-4">
          <div className="flex items-center gap-3 text-left">
            <div
              className={cn(
                'size-10 rounded-full bg-gradient-to-r flex items-center justify-center',
                gradient
              )}
            >
              <Filter size={20} color="white" />
            </div>
            <div>
              <DrawerTitle className="text-primary-text text-lg">
                {drawerTitles[category]}
              </DrawerTitle>
              <DrawerDescription className="text-secondary-text">
                {activeFiltersCount > 0
                  ? `${activeFiltersCount} ${pluralize('filter', activeFiltersCount)} active`
                  : 'Customize your search'}
              </DrawerDescription>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {!isEqual(filters, queryFilters) && (
              <IconButton
                Icon={RotateCcw}
                text="Reset"
                variant="glass"
                size="sm"
                onClick={handleClickResetLocal}
              />
            )}
            <IconButton
              Icon={X}
              variant="glass"
              size="sm"
              onClick={handleClose}
              className="border-none shadow-none p-2"
            />
          </div>
        </DrawerHeader>
        <Form {...form}>
          <form
            onSubmit={handleSubmit(onSubmit)}
            className="flex flex-col overflow-y-auto overflow-x-hidden *:not-last:mx-6 pt-6 gap-8 *:not-last:pb-8 bg-card-bg backdrop-blur-sm divide-y-[1px] divide-muted-foreground/40"
          >
            <DrawerSection
              title="💰 Price Range"
              badgeText={filters.priceLevel.length || undefined}
            >
              <FormField
                control={control}
                name="priceLevel"
                render={({ field: { value, onChange } }) => (
                  <div className="grid grid-cols-2 gap-3">
                    {filterOptions.priceLevels.map(level => (
                      <CheckboxCard
                        key={level}
                        name="priceLevel"
                        label={getPriceLevelText(level)}
                        checked={value.includes(level)}
                        onCheckedChange={checked =>
                          checked
                            ? onChange([...value, level])
                            : onChange(value.filter(l => l !== level))
                        }
                        gradient={gradient}
                      />
                    ))}
                  </div>
                )}
              />
            </DrawerSection>
            <DrawerSection
              title="⭐ Minimum Rating"
              badgeText={filters.rating > 0 ? `${filters.rating}+ stars` : undefined}
            >
              <FormField
                control={control}
                name="rating"
                render={({ field: { value, onChange } }) => (
                  <FormItem>
                    <FormControl>
                      <Slider
                        value={[value]}
                        onValueChange={val => onChange(val[0])}
                        min={0}
                        max={5}
                        step={0.5}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </DrawerSection>
            <DrawerSection
              title="🏷️ Atmosphere & Style"
              badgeText={filters.tags.length || undefined}
            >
              <FormField
                control={control}
                name="tags"
                render={({ field: { value, onChange } }) => (
                  <div className="flex flex-wrap gap-2">
                    {filterOptions.tags.map(tag => (
                      <CheckboxCard
                        key={tag}
                        name="tags"
                        label={tag}
                        checked={value.includes(tag)}
                        onCheckedChange={checked =>
                          onChange(checked ? [...value, tag] : value.filter(t => t !== tag))
                        }
                        gradient={gradient}
                        size="sm"
                      />
                    ))}
                  </div>
                )}
              />
            </DrawerSection>
            {filterOptions.categories.length > 1 && (
              <DrawerSection
                title="📍 Place Type"
                badgeText={filters.categories.length || undefined}
              >
                <FormField
                  control={control}
                  name="categories"
                  render={({ field: { value, onChange } }) => (
                    <div className="space-y-2">
                      {filterOptions.categories.map(cat => (
                        <CheckboxCard
                          key={cat}
                          name="categories"
                          label={cat}
                          checked={value.includes(cat)}
                          onCheckedChange={checked =>
                            onChange(checked ? [...value, cat] : value.filter(c => c !== cat))
                          }
                          gradient={gradient}
                        />
                      ))}
                    </div>
                  )}
                />
              </DrawerSection>
            )}
            <DrawerSection title="🚶 Travel Time" badgeText={filters.travelTime.text}>
              <FormField
                control={form.control}
                name="travelTime"
                render={({ field: { value, onChange } }) => (
                  <FormItem>
                    <FormControl>
                      <RadioGroup
                        value={value.text}
                        onValueChange={val =>
                          onChange({ text: val, minutes: getNumberFromText(val) })
                        }
                        className="grid grid-cols-2 gap-2"
                      >
                        {filterOptions.travelTimes.map(({ text }) => (
                          <RadioCard
                            key={text}
                            value={text}
                            label={text}
                            active={value.text === text}
                            gradient={gradient}
                          />
                        ))}
                      </RadioGroup>
                    </FormControl>
                  </FormItem>
                )}
              />
            </DrawerSection>
            <DrawerSection
              title="✨ Features & Amenities"
              badgeText={filters.amenities.length || undefined}
            >
              <FormField
                control={control}
                name="amenities"
                render={({ field: { value, onChange } }) => (
                  <div className="flex flex-wrap gap-2">
                    {filterOptions.amenities.map(amenity => (
                      <CheckboxCard
                        key={amenity}
                        name="amenities"
                        label={amenity}
                        checked={value.includes(amenity)}
                        onCheckedChange={checked =>
                          onChange(checked ? [...value, amenity] : value.filter(a => a !== amenity))
                        }
                        gradient={gradient}
                        size="sm"
                      />
                    ))}
                  </div>
                )}
              />
            </DrawerSection>
            <DrawerFooter className="flex-row bg-card-bg backdrop-blur-xl border-t border-muted-foreground/40 sticky bottom-0">
              <IconButton
                text="Reset All"
                Icon={RotateCcw}
                variant="card"
                className="rounded-md flex-1 gap-4 duration-300"
                onClick={handleClickResetAll}
                disabled={!activeFiltersCount}
              />
              <IconButton
                text="Apply Filters"
                Icon={Filter}
                badgeText={activeFiltersCount || undefined}
                variant="default"
                className={cn(
                  'rounded-md flex-1 gap-4 duration-300 bg-gradient-to-r text-white border-transparent hoverActive:opacity-80',
                  gradient
                )}
                type="submit"
                disabled={isEqual(filters, queryFilters)}
              />
            </DrawerFooter>
          </form>
        </Form>
      </DrawerContent>
    </Drawer>
  )
}

export default FilterDrawer
