import { FilterState, Recommendation } from '@/types/types'

export const defaultFilterState: FilterState = {
  priceLevel: [],
  rating: 0,
  tags: [],
  amenities: [],
  travelTime: { minutes: 0, text: 'All' },
  categories: [],
}

const TRAVEL_TIME_CATEGORIES = [
  { maxMinutes: 0, text: 'All' },
  { maxMinutes: 5, text: 'Very Close (≤5 min)' },
  { maxMinutes: 10, text: 'Close (≤10 min)' },
  { maxMinutes: 15, text: 'Moderate (≤15 min)' },
  { maxMinutes: Infinity, text: 'Further (>15 min)' },
] as const

const PRICE_LEVEL_MAP = {
  0: 'Free',
  1: 'Budget ($)',
  2: 'Moderate ($$)',
  3: 'Premium ($$$)',
} as const

/**
 * Get available filter options for a given category
 */
export function getFilterOptions(recommendations: Recommendation[]) {
  const options = recommendations.reduce(
    (acc, rec) => {
      rec.tags.forEach(tag => acc.tags.add(tag))
      rec.amenities.forEach(amenity => acc.amenities.add(amenity))
      acc.categories.add(rec.category)
      acc.priceLevels.add(rec.priceLevel)
      const travelCategory = getTravelTimeCategory(rec.travelTime)
      acc.travelTimes.add(travelCategory.text)

      return acc
    },
    {
      tags: new Set<string>(),
      amenities: new Set<string>(),
      categories: new Set<string>(),
      travelTimes: new Set<string>(),
      priceLevels: new Set<number>(),
    }
  )

  const travelTimes = Array.from([getTravelTimeCategory(0).text, ...options.travelTimes])
    .map(text => ({ minutes: getNumberFromText(text), text }))
    .sort((a, b) => a.minutes - b.minutes)

  return {
    tags: Array.from(options.tags).sort(),
    amenities: Array.from(options.amenities).sort(),
    categories: Array.from(options.categories).sort(),
    travelTimes,
    priceLevels: Array.from(options.priceLevels).sort(),
    maxRating: 5,
  }
}

/**
 * Apply filters to recommendations
 */
export function applyFilters(
  recommendations: Recommendation[],
  filters: FilterState
): Recommendation[] {
  return recommendations.filter(({ priceLevel, rating, tags, amenities, travelTime, category }) => {
    if (filters.priceLevel.length > 0 && !filters.priceLevel.includes(priceLevel)) {
      return false
    }

    if (filters.rating > 0 && rating < filters.rating) {
      return false
    }

    if (filters.tags.length > 0) {
      const hasMatchingTag = filters.tags.some(tag =>
        tags.some(recTag => recTag.toLowerCase().includes(tag.toLowerCase()))
      )
      if (!hasMatchingTag) return false
    }

    if (filters.amenities.length > 0) {
      const hasMatchingAmenity = filters.amenities.some(amenity =>
        amenities.some(recAmenity => recAmenity.toLowerCase().includes(amenity.toLowerCase()))
      )
      if (!hasMatchingAmenity) return false
    }

    if (filters.travelTime.minutes > 0 && travelTime > filters.travelTime.minutes) {
      return false
    }

    if (filters.categories.length > 0 && !filters.categories.includes(category)) {
      return false
    }

    if (filters.travelTime.minutes > 0 && travelTime > filters.travelTime.minutes) {
      return false
    }

    return true
  })
}

/**
 * Count active filters
 */
export function getActiveFiltersCount(filters: FilterState): number {
  const activeFilters = [
    filters.priceLevel.length > 0,
    filters.rating > 0,
    filters.tags.length > 0,
    filters.amenities.length > 0,
    filters.travelTime.minutes > 0,
    filters.categories.length > 0,
  ]

  return activeFilters.filter(Boolean).length
}

/**
 * Get price level display text
 */
export function getPriceLevelText(level: number): string {
  return (
    PRICE_LEVEL_MAP[level as keyof typeof PRICE_LEVEL_MAP] ??
    `$${'$'.repeat(Math.max(0, level - 1))}`
  )
}

/**
 * Get travel time category
 */
export function getTravelTimeCategory(minutes: string | number) {
  const timeInMinutes = typeof minutes === 'string' ? getNumberFromText(minutes) : minutes

  const category =
    TRAVEL_TIME_CATEGORIES.find(cat => timeInMinutes <= cat.maxMinutes) ??
    TRAVEL_TIME_CATEGORIES[TRAVEL_TIME_CATEGORIES.length - 1]

  return { minutes: timeInMinutes, text: category.text }
}

export function getNumberFromText(text: string) {
  const minutes = parseInt(text.match(/\d+/)?.[0] || '0')
  return minutes
}
