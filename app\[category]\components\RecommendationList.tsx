'use client'

import { useState } from 'react'
import { Category, Recommendation } from '@/types/types'
import useRecommendationListType from '@/hooks/useRecommendationListType'
import IconBadge from '@/components/IconBadge'
import { Badge } from '@/components/UI/badge'
import { Sparkles } from 'lucide-react'
import RecommendationCard from './RecommendationCard'
import MapView from './MapView'
import { getCategoryDetailsById } from '@/lib/categoryHelper'
import SearchFilterCard from './SearchFilterCard'
import pluralize from 'pluralize'

type Props = {
  category: Category
  recommendations: Recommendation[]
}

const RecommendationList = ({ category, recommendations }: Props) => {
  console.log('recommendations')
  const [categoryDetails] = useState(() => getCategoryDetailsById(category))
  const { recommendationListType } = useRecommendationListType()
  const showMap = recommendationListType === 'list'

  return (
    <section className="relative flex flex-col gap-6 py-4 max-sm:px-4 max-w-md mx-auto">
      {showMap && (
        <>
          <MapView
            category={category}
            recommendations={recommendations}
            gradient={categoryDetails.gradient}
          />
          <div className="flex justify-between *:duration-300 opacity-0 animate-fade-up-transform">
            <h3 className="text-xl font-semibold">Nearby Locations</h3>
            <Badge variant="card" className="rounded-md text-accent-text" size="sm">
              {recommendations.length} {pluralize('place', recommendations.length)}
            </Badge>
          </div>
        </>
      )}
      <SearchFilterCard
        numberOfRecommendations={recommendations.length}
        gradient={categoryDetails.gradient}
      />
      <div className="flex flex-col gap-4">
        {recommendations.map((recommendation, index) => (
          <RecommendationCard
            key={recommendation.id}
            categoryDetails={categoryDetails}
            {...recommendation}
            index={index}
            showDetails={!showMap}
          />
        ))}
      </div>
      <IconBadge
        Icon={Sparkles}
        variant="card"
        text="More discoveries coming soon"
        className="hoverActive:scale-105 duration-700 mx-auto"
        iconClassName="text-yellow-400"
        size="lg"
      />
    </section>
  )
}

export default RecommendationList
