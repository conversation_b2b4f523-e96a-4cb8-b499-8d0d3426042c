import { notFound } from 'next/navigation'
import { mockRecommendations } from '@/data/mockData'
import RecommendationListHeader from './components/RecommendationListHeader'
import RecommendationList from './components/RecommendationList'
import CategoryHeader from './components/CategoryHeader'
import { categories as categoriesArray, Category, FilterState } from '@/types/types'
import RecommendationListTypeProvider from '@/components/RecommendationListTypeProvider'
import FilterDrawer from './components/FilterDrawer'
import { searchAndFilterRecommendations, searchRecommendations } from '@/lib/searchUtils'
import { defaultFilterState } from '@/lib/filterUtils'
import isEqual from 'lodash/isEqual'
import { searchParamsCache } from '@/lib/searchParams'
import type { SearchParams } from 'nuqs/server'

type Props = {
  params: Promise<{ category: string }>
  searchParams: Promise<SearchParams>
}

const CategoryPage = async ({ params, searchParams }: Props) => {
  const { category } = await params
  const { q, priceLevel, rating, tags, amenities, travelTime, categories } =
    await searchParamsCache.parse(searchParams)

  if (!categoriesArray.includes(category as Category)) {
    notFound()
  }

  const parsedFilters: FilterState = {
    priceLevel,
    rating,
    tags,
    amenities,
    travelTime,
    categories,
  }

  const recommendations = mockRecommendations[category as Category]
  const filteredRecommendations = !isEqual(parsedFilters, defaultFilterState)
    ? searchAndFilterRecommendations(recommendations, q || '', parsedFilters)
    : searchRecommendations(recommendations, q || '')

  console.log('category', category)
  console.log('q', q)
  console.log('parsedFilters', parsedFilters)
  console.log('filteredRecommendations', filteredRecommendations)

  return (
    <RecommendationListTypeProvider>
      <CategoryHeader
        category={category as Category}
        numberOfRecommendations={filteredRecommendations.length}
      />
      <main>
        <RecommendationListHeader
          category={category as Category}
          numberOfRecommendations={filteredRecommendations.length}
          searchParamsFilters={parsedFilters}
        />
        <RecommendationList
          category={category as Category}
          recommendations={filteredRecommendations}
        />
      </main>
      <FilterDrawer category={category as Category} recommendations={recommendations} />
    </RecommendationListTypeProvider>
  )
}

export default CategoryPage
